import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";
import { JWT_LIVE_TIME } from "@/constants/common";
import {
  handleCallbackProvider,
  handleLoginSuccess,
  callTurnkeyWhoami,
} from "../../_helpers";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get("code");
  const error = searchParams.get("error");

  if (error) {
    return NextResponse.redirect(
      new URL(`/auth/error?error=${error}`, request.url)
    );
  }

  if (!code) {
    return NextResponse.redirect(
      new URL("/auth/error?error=MissingCode", request.url)
    );
  }

  try {
    // Exchange code for tokens
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        code,
        grant_type: "authorization_code",
        redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/auth/google/callback`,
      }),
    });

    const tokens = await tokenResponse.json();
    if (!tokens.id_token) {
      throw new Error("No ID token received");
    }

    // Validate nonce
    const decoded: any = jwtDecode(tokens.id_token);
    const idTokenNonce = decoded?.nonce;
    const cookieStore = await cookies();
    const expectedNonce = cookieStore.get("turnkey_login_nonce")?.value;
    const publicKey = cookieStore.get("turnkey_login_public_key")?.value;

    if (!idTokenNonce || !expectedNonce || idTokenNonce !== expectedNonce) {
      return NextResponse.redirect(
        new URL("/auth/error?error=InvalidNonce", request.url)
      );
    }

    // Process authentication
    const email = decoded?.email;
    const result = await handleCallbackProvider(
      tokens.id_token,
      "google",
      email,
      publicKey
    );

    // Save complete Turnkey login response to secure cookies
    cookieStore.set("turnkey_login_response", JSON.stringify(result), {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME), // Same expiration as JWT
    });

    // Save individual credentials for easy access
    cookieStore.set("turnkey_organization_id", result.subOrganizationId, {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    });

    cookieStore.set("turnkey_credential_bundle", result.credentialBundle, {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    });

    // Call Turnkey whoami to get user information
    try {
      const whoamiData = await callTurnkeyWhoami(
        result.subOrganizationId,
        result.credentialBundle
      );

      console.log("Turnkey whoami successful:", {
        userId: whoamiData.userId,
        organizationId: whoamiData.organizationId,
        userEmail: whoamiData.userEmail,
        username: whoamiData.username,
      });

      // Store whoami response in cookies as well
      cookieStore.set("turnkey_whoami_response", JSON.stringify(whoamiData), {
        httpOnly: true,
        secure: true,
        sameSite: "lax",
        path: "/",
        expires: new Date(Date.now() + JWT_LIVE_TIME),
      });
    } catch (whoamiError: any) {
      console.error("Turnkey whoami failed:", whoamiError.message);
      // Continue with login even if whoami fails, but log the error
      // You might want to handle this differently based on your requirements
    }

    await handleLoginSuccess(result?.jwtToken);

    // Clear nonce and public key cookies
    cookieStore.set("turnkey_login_nonce", "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    cookieStore.set("turnkey_login_public_key", "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    const redirectUrl = new URL("/auth/finalize", request.url);
    redirectUrl.searchParams.set("jwt", result.jwtToken);
    return NextResponse.redirect(redirectUrl.toString());
  } catch (error: any) {
    console.error("OAuth callback error:", error);
    return NextResponse.redirect(
      new URL(
        `/auth/error?error=${encodeURIComponent(
          error?.message || "AuthFailed"
        )}`,
        request.url
      )
    );
  }
}
