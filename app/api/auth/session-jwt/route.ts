import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants/common";

export async function GET() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIES_ACCESS_TOKEN_KEY)?.value;
    if (!token) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }
    return NextResponse.json({ jwtToken: token }, { status: 200 });
  } catch (e: any) {
    return NextResponse.json(
      { error: e?.message || "Failed to retrieve session token" },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    const response = NextResponse.json({ ok: true }, { status: 200 });

    // Clear JWT token
    response.cookies.set(COOKIES_ACCESS_TOKEN_KEY, "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    // Clear Turnkey credentials
    response.cookies.set("turnkey_organization_id", "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    response.cookies.set("turnkey_credential_bundle", "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    return response;
  } catch (e: any) {
    return NextResponse.json(
      { error: e?.message || "Failed to clear session token" },
      { status: 500 }
    );
  }
}
