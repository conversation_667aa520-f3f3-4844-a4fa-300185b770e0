import rf from "@/services/RequestFactory";
import { COOKIES_ACCESS_TOKEN_KEY, JWT_LIVE_TIME } from "@/constants/common";
import { TurnkeyLoginResponse } from "@/types/turnkey.type";

export async function handleCallbackProvider(
  token: string,
  provider: string,
  email?: string | null,
  publicKey?: string
): Promise<TurnkeyLoginResponse> {
  const payload: any = {
    oauthProvider: { oidcToken: token, providerName: provider },
  };
  if (email) payload.email = email;
  if (publicKey) payload.publicKey = publicKey;

  const res: any = await rf
    .getRequest("TurnkeyAuthRequest")
    .exchangeTurnkeySession(payload);

  if (!res?.jwtToken) {
    throw new Error("Backend did not return a jwtToken");
  }

  return res;
}

export async function handleLoginSuccess(jwtToken?: string) {
  if (!jwtToken) throw new Error("Missing jwtToken");

  try {
    const { cookies } = await import("next/headers");
    const cookieStore = await cookies();
    cookieStore.set({
      name: COOKIES_ACCESS_TOKEN_KEY,
      value: jwtToken,
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    } as any);
  } catch {}

  return { jwtToken };
}
