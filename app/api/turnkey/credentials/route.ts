import { NextResponse } from "next/server";
import { cookies } from "next/headers";

/**
 * API endpoint to retrieve stored Turnkey credentials from cookies
 * GET /api/turnkey/credentials
 * 
 * Returns the organization ID and credential bundle stored in secure cookies
 * after successful Google OAuth login
 */
export async function GET() {
  try {
    const cookieStore = await cookies();
    const organizationId = cookieStore.get("turnkey_organization_id")?.value;
    const credentialBundle = cookieStore.get("turnkey_credential_bundle")?.value;

    if (!organizationId || !credentialBundle) {
      return NextResponse.json(
        { 
          error: "No Turnkey credentials found. Please complete Google OAuth login first.",
          hasCredentials: false,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      hasCredentials: true,
      data: {
        organizationId,
        credentialBundle,
      },
    });
  } catch (error: any) {
    console.error("Error retrieving Turnkey credentials:", error);
    
    return NextResponse.json(
      {
        error: error.message || "Failed to retrieve Turnkey credentials",
        success: false,
        hasCredentials: false,
      },
      { status: 500 }
    );
  }
}

/**
 * API endpoint to clear stored Turnkey credentials
 * DELETE /api/turnkey/credentials
 * 
 * Clears the organization ID and credential bundle cookies
 */
export async function DELETE() {
  try {
    const cookieStore = await cookies();
    
    // Clear the Turnkey credential cookies
    cookieStore.set("turnkey_organization_id", "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    cookieStore.set("turnkey_credential_bundle", "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });

    return NextResponse.json({
      success: true,
      message: "Turnkey credentials cleared successfully",
    });
  } catch (error: any) {
    console.error("Error clearing Turnkey credentials:", error);
    
    return NextResponse.json(
      {
        error: error.message || "Failed to clear Turnkey credentials",
        success: false,
      },
      { status: 500 }
    );
  }
}
