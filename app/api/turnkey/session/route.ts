import { NextResponse } from "next/server";

export async function GET() {
  try {
    const jwtToken =
      typeof crypto?.randomUUID === "function"
        ? crypto.randomUUID()
        : `${Date.now()}-${Math.random().toString(36).slice(2)}`;
    return NextResponse.json({ jwtToken }, { status: 200 });
  } catch (e: any) {
    return NextResponse.json(
      { error: e?.message || "Failed to create Turnkey session token" },
      { status: 500 }
    );
  }
}
