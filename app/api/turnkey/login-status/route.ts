import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { callTurnkeyWhoami } from "../../auth/_helpers";

/**
 * API endpoint to get complete Turnkey login status and data
 * GET /api/turnkey/login-status
 * 
 * Returns:
 * - Login response data
 * - Stored whoami response (if available)
 * - Fresh whoami call (if credentials are available)
 * - Login status and credential availability
 */
export async function GET() {
  try {
    const cookieStore = await cookies();
    const organizationId = cookieStore.get("turnkey_organization_id")?.value;
    const credentialBundle = cookieStore.get("turnkey_credential_bundle")?.value;
    const loginResponseStr = cookieStore.get("turnkey_login_response")?.value;
    const storedWhoamiStr = cookieStore.get("turnkey_whoami_response")?.value;

    // Parse stored data
    let loginResponse = null;
    let storedWhoamiResponse = null;

    try {
      if (loginResponseStr) {
        loginResponse = JSON.parse(loginResponseStr);
      }
    } catch (e) {
      console.warn("Failed to parse login response from cookie");
    }

    try {
      if (storedWhoamiStr) {
        storedWhoamiResponse = JSON.parse(storedWhoamiStr);
      }
    } catch (e) {
      console.warn("Failed to parse stored whoami response from cookie");
    }

    const hasCredentials = !!(organizationId && credentialBundle);
    let freshWhoamiResponse = null;
    let whoamiError = null;

    // If we have credentials, make a fresh whoami call
    if (hasCredentials) {
      try {
        freshWhoamiResponse = await callTurnkeyWhoami(organizationId, credentialBundle);
        
        // Update the stored whoami response with fresh data
        cookieStore.set("turnkey_whoami_response", JSON.stringify(freshWhoamiResponse), {
          httpOnly: true,
          secure: true,
          sameSite: "lax",
          path: "/",
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        });
      } catch (error: any) {
        whoamiError = error.message;
        console.error("Fresh whoami call failed:", error);
      }
    }

    return NextResponse.json({
      success: true,
      hasCredentials,
      data: {
        loginResponse,
        storedWhoamiResponse,
        freshWhoamiResponse,
        organizationId,
        // Don't return credential bundle for security
        credentialBundle: hasCredentials ? "[PRESENT]" : null,
      },
      whoamiError,
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error("Error getting Turnkey login status:", error);
    
    return NextResponse.json(
      {
        error: error.message || "Failed to get Turnkey login status",
        success: false,
        hasCredentials: false,
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint to manually trigger whoami call and update stored data
 * POST /api/turnkey/login-status
 * 
 * Forces a fresh whoami call using stored credentials
 */
export async function POST() {
  try {
    const cookieStore = await cookies();
    const organizationId = cookieStore.get("turnkey_organization_id")?.value;
    const credentialBundle = cookieStore.get("turnkey_credential_bundle")?.value;

    if (!organizationId || !credentialBundle) {
      return NextResponse.json(
        {
          error: "No stored credentials found. Please complete Google OAuth login first.",
          success: false,
          hasCredentials: false,
        },
        { status: 404 }
      );
    }

    // Make fresh whoami call
    const whoamiResponse = await callTurnkeyWhoami(organizationId, credentialBundle);
    
    // Update stored whoami response
    cookieStore.set("turnkey_whoami_response", JSON.stringify(whoamiResponse), {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });

    return NextResponse.json({
      success: true,
      data: whoamiResponse,
      message: "Whoami response updated successfully",
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error("Error updating whoami response:", error);
    
    return NextResponse.json(
      {
        error: error.message || "Failed to update whoami response",
        success: false,
      },
      { status: 500 }
    );
  }
}
