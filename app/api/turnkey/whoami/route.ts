import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { callTurnkeyWhoami } from "../../auth/_helpers";

/**
 * Test endpoint for Turnkey whoami functionality
 * POST /api/turnkey/whoami
 *
 * Body (optional): {
 *   organizationId?: string,
 *   credentialBundle?: string
 * }
 *
 * If no credentials are provided in the body, it will automatically
 * use the credentials stored in cookies from the last successful login
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    let { organizationId, credentialBundle } = body;

    // If credentials are not provided in the request body,
    // try to get them from cookies
    if (!organizationId || !credentialBundle) {
      const cookieStore = await cookies();
      const storedOrgId = cookieStore.get("turnkey_organization_id")?.value;
      const storedCredBundle = cookieStore.get(
        "turnkey_credential_bundle"
      )?.value;

      // Use stored credentials if available
      organizationId = organizationId || storedOrgId;
      credentialBundle = credentialBundle || storedCredBundle;

      if (!organizationId || !credentialBundle) {
        return NextResponse.json(
          {
            error:
              "No credentials provided and no stored credentials found. Please complete Google OAuth login first or provide credentials in the request body.",
            hasStoredCredentials: !!(storedOrgId && storedCredBundle),
          },
          { status: 400 }
        );
      }
    }

    const whoamiData = await callTurnkeyWhoami(
      organizationId,
      credentialBundle
    );
    console.log("Turnkey whoami data:", whoamiData);

    return NextResponse.json({
      success: true,
      data: whoamiData,
      usedStoredCredentials: !body.organizationId && !body.credentialBundle,
    });
  } catch (error: any) {
    console.error("Turnkey whoami API error:", error);

    return NextResponse.json(
      {
        error: error.message || "Failed to call Turnkey whoami",
        success: false,
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to call whoami using stored credentials only
 * GET /api/turnkey/whoami
 *
 * This endpoint only uses credentials stored in cookies from the last successful login
 */
export async function GET() {
  try {
    const cookieStore = await cookies();
    const organizationId = cookieStore.get("turnkey_organization_id")?.value;
    const credentialBundle = cookieStore.get(
      "turnkey_credential_bundle"
    )?.value;

    if (!organizationId || !credentialBundle) {
      return NextResponse.json(
        {
          error:
            "No stored credentials found. Please complete Google OAuth login first.",
          hasStoredCredentials: false,
        },
        { status: 404 }
      );
    }

    const whoamiData = await callTurnkeyWhoami(
      organizationId,
      credentialBundle
    );

    return NextResponse.json({
      success: true,
      data: whoamiData,
      usedStoredCredentials: true,
    });
  } catch (error: any) {
    console.error("Turnkey whoami GET API error:", error);

    return NextResponse.json(
      {
        error: error.message || "Failed to call Turnkey whoami",
        success: false,
      },
      { status: 500 }
    );
  }
}
