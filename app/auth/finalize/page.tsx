"use client";

import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { setUserAuth, setIsExternalWallet } from "@/store/user.store";
import Storage from "@/libs/storage";
import { createSocketInstance, closeSocketInstance } from "@/libs/socket";
import { NETWORKS } from "@/utils/contants";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { useSearchParams } from "next/navigation";

export default function AuthFinalizePage() {
  const dispatch = useDispatch<AppDispatch>();
  const [error, setError] = useState<string>("");
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();
  const searchParams = useSearchParams();

  useEffect(() => {
    const finalize = async () => {
      try {
        let jwtToken = searchParams.get("jwt");

        if (!jwtToken) {
          const res = await fetch("/api/auth/session-jwt", {
            cache: "no-store",
          });
          if (res.ok) {
            const data = await res.json();
            jwtToken = data?.jwtToken;
          }
        }

        if (!jwtToken) throw new Error("Missing JWT token");

        dispatch(setUserAuth({ accessToken: jwtToken }));

        if (currentAccount?.address) {
          disconnect();
        }

        Storage.setLoginMethod("turnkey");
        dispatch(setIsExternalWallet(false));

        const redirectAfterLogin = Storage.getRedirectAfterLogin();
        if (redirectAfterLogin) {
          const location = `${window.location.pathname}${window.location.search}`;
          if (location !== redirectAfterLogin) {
            Storage.clearRedirectAfterLogin();
            window.location.href = redirectAfterLogin;
            return;
          }
        }

        closeSocketInstance(NETWORKS.SUI);
        createSocketInstance(NETWORKS.SUI, jwtToken);

        try {
          await fetch("/api/auth/session-jwt", { method: "DELETE" });
        } catch {}

        window.location.href = "/new-pairs";
      } catch (e: any) {
        console.error("Authentication finalization error:", e);
        setError(e?.message || "Authentication failed");
      }
    };

    finalize();
  }, [dispatch, currentAccount?.address, disconnect, searchParams]);

  return (
    <div className="flex min-h-[60vh] items-center justify-center px-6">
      <div className="text-center">
        <div className="text-white-1000 mb-2 text-lg font-semibold">
          Completing sign-in…
        </div>
        <div className="text-white-500 text-sm">
          {error ? `Error: ${error}` : "Please wait"}
        </div>
      </div>
    </div>
  );
}
