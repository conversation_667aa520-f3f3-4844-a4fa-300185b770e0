"use client";

import React, { useState } from "react";
import { useTurnkeyWhoami } from "@/hooks/useTurnkeyWhoami";

/**
 * Example component demonstrating Turnkey whoami functionality
 * This component shows how to:
 * 1. Call the whoami endpoint with organization ID and credential bundle
 * 2. Handle loading states and errors
 * 3. Display user information returned from Turnkey
 */
export const TurnkeyWhoamiExample: React.FC = () => {
  const {
    whoamiData,
    loading,
    error,
    hasStoredCredentials,
    usedStoredCredentials,
    callWhoami,
    callWhoamiWithStoredCredentials,
    clearData,
    checkStoredCredentials,
  } = useTurnkeyWhoami();
  const [organizationId, setOrganizationId] = useState("");
  const [credentialBundle, setCredentialBundle] = useState("");

  // Check for stored credentials on component mount
  React.useEffect(() => {
    checkStoredCredentials();
  }, [checkStoredCredentials]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await callWhoami(organizationId, credentialBundle);
  };

  const handleUseStoredCredentials = async () => {
    await callWhoamiWithStoredCredentials();
  };

  const handleClear = () => {
    clearData();
    setOrganizationId("");
    setCredentialBundle("");
  };

  const handleClearStoredCredentials = async () => {
    try {
      await fetch("/api/turnkey/credentials", { method: "DELETE" });
      await checkStoredCredentials();
      clearData();
    } catch (err) {
      console.error("Failed to clear stored credentials:", err);
    }
  };

  return (
    <div className="mx-auto max-w-2xl rounded-lg bg-white p-6 shadow-lg">
      <h2 className="mb-6 text-2xl font-bold text-gray-800">
        Turnkey Whoami Test
      </h2>

      {/* Stored Credentials Section */}
      <div className="mb-6 rounded-md border border-blue-200 bg-blue-50 p-4">
        <h3 className="mb-3 font-semibold text-blue-800">Stored Credentials</h3>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-blue-700">
              {hasStoredCredentials === null
                ? "Checking for stored credentials..."
                : hasStoredCredentials
                ? "✅ Credentials from last login are available"
                : "❌ No stored credentials found. Please complete Google OAuth login first."}
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleUseStoredCredentials}
              disabled={loading || !hasStoredCredentials}
              className="rounded bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:bg-gray-400"
            >
              {loading ? "Testing..." : "Test Stored Credentials"}
            </button>
            <button
              type="button"
              onClick={handleClearStoredCredentials}
              disabled={loading || !hasStoredCredentials}
              className="rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700 disabled:cursor-not-allowed disabled:bg-gray-400"
            >
              Clear Stored
            </button>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="mb-6 space-y-4">
        <div>
          <label
            htmlFor="organizationId"
            className="mb-2 block text-sm font-medium text-gray-700"
          >
            Organization ID
          </label>
          <input
            type="text"
            id="organizationId"
            value={organizationId}
            onChange={(e) => setOrganizationId(e.target.value)}
            placeholder="Enter organization ID from login response"
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label
            htmlFor="credentialBundle"
            className="mb-2 block text-sm font-medium text-gray-700"
          >
            Credential Bundle
          </label>
          <textarea
            id="credentialBundle"
            value={credentialBundle}
            onChange={(e) => setCredentialBundle(e.target.value)}
            placeholder="Enter credential bundle JSON from login response"
            rows={4}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div className="flex space-x-4">
          <button
            type="submit"
            disabled={loading || !organizationId || !credentialBundle}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:bg-gray-400"
          >
            {loading ? "Calling Whoami..." : "Call Whoami"}
          </button>

          <button
            type="button"
            onClick={handleClear}
            className="rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
          >
            Clear
          </button>
        </div>
      </form>

      {/* Error Display */}
      {error && (
        <div className="mb-6 rounded-md border border-red-400 bg-red-100 p-4 text-red-700">
          <h3 className="mb-2 font-semibold">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {/* Success Display */}
      {whoamiData && (
        <div className="mb-6 rounded-md border border-green-400 bg-green-100 p-4 text-green-700">
          <h3 className="mb-4 font-semibold">
            Whoami Response{" "}
            {usedStoredCredentials && "(using stored credentials)"}:
          </h3>
          <div className="space-y-2">
            <div>
              <span className="font-medium">User ID:</span> {whoamiData.userId}
            </div>
            <div>
              <span className="font-medium">Username:</span>{" "}
              {whoamiData.username || "N/A"}
            </div>
            <div>
              <span className="font-medium">Email:</span>{" "}
              {whoamiData.userEmail || "N/A"}
            </div>
            <div>
              <span className="font-medium">Organization ID:</span>{" "}
              {whoamiData.organizationId}
            </div>
            <div>
              <span className="font-medium">Organization Name:</span>{" "}
              {whoamiData.organizationName || "N/A"}
            </div>
            <div>
              <span className="font-medium">User Tags:</span>{" "}
              {whoamiData.userTags.length > 0
                ? whoamiData.userTags.join(", ")
                : "None"}
            </div>
          </div>
        </div>
      )}

      {/* Usage Instructions */}
      <div className="mt-8 rounded-md border border-blue-200 bg-blue-50 p-4">
        <h3 className="mb-2 font-semibold text-blue-800">
          Usage Instructions:
        </h3>
        <ol className="list-inside list-decimal space-y-1 text-sm text-blue-700">
          <li>
            <strong>Automatic (Recommended):</strong> Complete Google OAuth
            login, then click "Test Stored Credentials" to use automatically
            saved credentials
          </li>
          <li>
            <strong>Manual:</strong> Enter organization ID and credential bundle
            manually in the form below
          </li>
          <li>
            The response will show user details including ID, email, and
            organization info
          </li>
          <li>
            Stored credentials are automatically cleared when you log out or
            clear them manually
          </li>
          <li>
            If no stored credentials are found, you'll need to complete Google
            OAuth login first
          </li>
        </ol>
      </div>
    </div>
  );
};
