# Turnkey Whoami Implementation

This document provides a complete implementation guide for integrating <PERSON><PERSON>'s `whoami` endpoint into your Google OAuth authentication flow.

## Overview

The implementation includes:
1. Server-side `callTurnkeyWhoami` function that handles the API call
2. Integration with the existing Google OAuth callback route
3. **Secure credential storage** - Organization ID and credential bundle saved to HTTP-only cookies
4. **Automatic credential retrieval** - Test endpoints can use stored credentials automatically
5. Frontend hook for calling whoami from client-side
6. Test API endpoint for debugging
7. Example component demonstrating usage
8. **Automatic cleanup** - Credentials cleared on logout

## Key Features

- **Organization ID Specification**: Always includes the specific organization ID to avoid "multiple sub-organizations" errors
- **Proper Authentication**: Uses ApiKeyStamper with credentials from the credential bundle
- **Secure Credential Storage**: Organization ID and credential bundle stored in HTTP-only cookies with proper security settings
- **Automatic Credential Management**: Test functionality automatically uses stored credentials from real login sessions
- **Error Handling**: Comprehensive error handling for common scenarios
- **Type Safety**: Full TypeScript support with proper type definitions
- **Integration**: Seamlessly integrates with existing authentication patterns
- **Automatic Cleanup**: Credentials automatically cleared on logout

## Files Modified/Created

### Core Implementation
- `app/api/auth/_helpers.ts` - Added `callTurnkeyWhoami` function
- `app/api/auth/google/callback/route.ts` - Integrated whoami call and credential storage into OAuth flow
- `app/api/auth/session-jwt/route.ts` - Updated to clear Turnkey credentials on logout
- `types/turnkey.type.ts` - Added `TurnkeyWhoamiResponse` type

### Credential Management
- `app/api/turnkey/credentials/route.ts` - API to retrieve and clear stored credentials
- Secure HTTP-only cookies for organization ID and credential bundle
- Automatic cleanup on logout

### Testing & Examples
- `app/api/turnkey/whoami/route.ts` - Test API endpoint (supports both manual and stored credentials)
- `hooks/useTurnkeyWhoami.ts` - Frontend hook with automatic credential detection
- `components/TurnkeyWhoamiExample.tsx` - Example component with stored credential support
- `app/test-turnkey/page.tsx` - Test page

## Usage in Authentication Flow

After successful Google OAuth login, the callback route now:

1. Exchanges the OAuth token for Turnkey credentials
2. **Saves organization ID and credential bundle to secure HTTP-only cookies**
3. Calls `callTurnkeyWhoami` with the organization ID and credential bundle
4. Logs the user information (can be stored or passed to frontend)
5. Continues with the normal login flow

```typescript
// In app/api/auth/google/callback/route.ts
const result = await handleCallbackProvider(tokens.id_token, "google", email, publicKey);

// Call Turnkey whoami to get user information
try {
  const whoamiData = await callTurnkeyWhoami(
    result.subOrganizationId,
    result.credentialBundle
  );
  
  console.log("User info:", whoamiData);
  // Store or use the user information as needed
} catch (whoamiError) {
  console.error("Whoami failed:", whoamiError.message);
  // Handle error appropriately
}
```

## API Response Structure

The `callTurnkeyWhoami` function returns:

```typescript
interface TurnkeyWhoamiResponse {
  userId: string;
  username?: string;
  userEmail?: string;
  organizationId: string;
  organizationName?: string;
  userTags: string[];
}
```

## Error Handling

The implementation handles these specific error scenarios:

1. **Multiple Sub-Organizations**: When the same public key is associated with multiple sub-organizations
2. **Organization Not Found**: When the specified organization ID doesn't exist or access is denied
3. **Authentication Failures**: When credentials are invalid or expired
4. **Invalid Credential Bundle**: When the credential bundle is malformed

## Testing

### Using the Test API Endpoint

```bash
curl -X POST http://localhost:3000/api/turnkey/whoami \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "your-org-id",
    "credentialBundle": "{\"apiKeyId\":\"...\",\"privateKey\":\"...\"}"
  }'
```

### Using the Frontend Hook

```typescript
import { useTurnkeyWhoami } from "@/hooks/useTurnkeyWhoami";

function MyComponent() {
  const { whoamiData, loading, error, callWhoami } = useTurnkeyWhoami();
  
  const handleWhoami = async () => {
    await callWhoami(organizationId, credentialBundle);
  };
  
  return (
    <div>
      {loading && <p>Loading...</p>}
      {error && <p>Error: {error}</p>}
      {whoamiData && <p>User: {whoamiData.userEmail}</p>}
    </div>
  );
}
```

## Dependencies

The implementation requires these packages:
- `@turnkey/http` - Core Turnkey HTTP client
- `@turnkey/api-key-stamper` - For API key authentication

Install with:
```bash
npm install @turnkey/http @turnkey/api-key-stamper --legacy-peer-deps
```

## Best Practices

1. **Always specify organization ID** in whoami calls to avoid ambiguity
2. **Handle errors gracefully** - don't let whoami failures break the login flow
3. **Validate credential bundle** before making API calls
4. **Log errors** for debugging but don't expose sensitive information
5. **Use the specific organization ID** from the login response, not a default one

## Integration Points

The whoami call integrates with your existing patterns:
- Uses the same error handling patterns as other API calls
- Follows the Redux/JWT storage patterns
- Maintains compatibility with existing authentication flows
- Can be extended to store user information in your database

## Testing the Implementation

### 1. Automatic Testing (Recommended)
1. Complete a Google OAuth login through your application
2. Navigate to `/test-turnkey` to access the test page
3. Click "Test Stored Credentials" to use the automatically saved credentials
4. Verify the whoami response shows your user information

### 2. Manual Testing Steps
1. Complete a Google OAuth login to get organization ID and credential bundle
2. Check the browser console for the logged whoami response
3. Use the test page to manually test with different credentials
4. Verify error handling with invalid credentials

### 3. API Testing
```bash
# Test with stored credentials (after login)
curl -X GET http://localhost:3000/api/turnkey/whoami

# Test with manual credentials
curl -X POST http://localhost:3000/api/turnkey/whoami \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "your-org-id",
    "credentialBundle": "{\"apiKeyId\":\"...\",\"privateKey\":\"...\"}"
  }'

# Check stored credentials
curl -X GET http://localhost:3000/api/turnkey/credentials

# Clear stored credentials
curl -X DELETE http://localhost:3000/api/turnkey/credentials
```

### 4. Integration Testing
The whoami call is automatically triggered during Google OAuth login. Check the server logs to see the results.

## Troubleshooting

### Common Issues
1. **"Invalid credential bundle"** - Ensure the credential bundle is valid JSON with apiKeyId and privateKey
2. **"Organization not found"** - Verify the organization ID is correct and accessible
3. **"Multiple sub-organizations"** - This is handled by specifying the exact organization ID
4. **Network errors** - Check that the Turnkey API base URL is correct

### Debug Steps
1. Check server console logs for detailed error messages
2. Use the test API endpoint to isolate issues
3. Verify the credential bundle structure
4. Confirm organization ID format

## Next Steps

1. Test the implementation with your actual Turnkey credentials
2. Decide how to store/use the user information returned by whoami
3. Consider adding the user information to your Redux store
4. Implement any additional error handling specific to your use case
5. Remove the test page and components when no longer needed
