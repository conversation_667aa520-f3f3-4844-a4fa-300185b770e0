"use client";

import { useState, useCallback } from "react";
import { TurnkeyWhoamiResponse } from "@/types/turnkey.type";

export interface UseTurnkeyWhoamiResult {
  whoamiData: TurnkeyWhoamiResponse | null;
  loading: boolean;
  error: string | null;
  hasStoredCredentials: boolean | null;
  usedStoredCredentials: boolean;
  callWhoami: (
    organizationId?: string,
    credentialBundle?: string
  ) => Promise<void>;
  callWhoamiWithStoredCredentials: () => Promise<void>;
  clearData: () => void;
  checkStoredCredentials: () => Promise<void>;
}

/**
 * Hook for calling Turnkey whoami endpoint from the frontend
 * This hook provides a convenient way to retrieve user information from Turnkey
 * It can use either provided credentials or automatically use stored credentials from cookies
 */
export function useTurnkeyWhoami(): UseTurnkeyWhoamiResult {
  const [whoamiData, setWhoamiData] = useState<TurnkeyWhoamiResponse | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasStoredCredentials, setHasStoredCredentials] = useState<
    boolean | null
  >(null);
  const [usedStoredCredentials, setUsedStoredCredentials] = useState(false);

  const callWhoami = useCallback(
    async (organizationId?: string, credentialBundle?: string) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch("/api/turnkey/whoami", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            organizationId,
            credentialBundle,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          setHasStoredCredentials(result.hasStoredCredentials || false);
          throw new Error(result.error || "Failed to call whoami");
        }

        if (result.success && result.data) {
          setWhoamiData(result.data);
          setUsedStoredCredentials(result.usedStoredCredentials || false);
          setError(null);
        } else {
          throw new Error("Invalid response format");
        }
      } catch (err: any) {
        console.error("Turnkey whoami hook error:", err);
        setError(err.message || "Failed to retrieve user information");
        setWhoamiData(null);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const callWhoamiWithStoredCredentials = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/turnkey/whoami", {
        method: "GET",
      });

      const result = await response.json();

      if (!response.ok) {
        setHasStoredCredentials(result.hasStoredCredentials || false);
        throw new Error(result.error || "Failed to call whoami");
      }

      if (result.success && result.data) {
        setWhoamiData(result.data);
        setUsedStoredCredentials(true);
        setHasStoredCredentials(true);
        setError(null);
      } else {
        throw new Error("Invalid response format");
      }
    } catch (err: any) {
      console.error("Turnkey whoami hook error:", err);
      setError(err.message || "Failed to retrieve user information");
      setWhoamiData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const checkStoredCredentials = useCallback(async () => {
    try {
      const response = await fetch("/api/turnkey/credentials");
      const result = await response.json();
      setHasStoredCredentials(result.hasCredentials || false);
    } catch (err) {
      setHasStoredCredentials(false);
    }
  }, []);

  const clearData = useCallback(() => {
    setWhoamiData(null);
    setError(null);
    setLoading(false);
    setUsedStoredCredentials(false);
  }, []);

  return {
    whoamiData,
    loading,
    error,
    hasStoredCredentials,
    usedStoredCredentials,
    callWhoami,
    callWhoamiWithStoredCredentials,
    clearData,
    checkStoredCredentials,
  };
}
